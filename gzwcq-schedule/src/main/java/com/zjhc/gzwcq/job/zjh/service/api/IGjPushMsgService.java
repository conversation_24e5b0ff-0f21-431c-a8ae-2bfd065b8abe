package com.zjhc.gzwcq.job.zjh.service.api;

import com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/27:15:18:07
 **/
public interface IGjPushMsgService {
    void pushMsg();
    /**
     * @description: TODO 初始化推送信息
     * @author: hhy
     * @date: 2025/6/13 14:12
     * @param
     * @return java.util.List<com.zjhc.gzwcq.job.zjh.entity.gjPush.bo.JbxxbBO>
     */
    List<JbxxbBO> initMsg();
    List<JbxxbBO> checkMsg(List<JbxxbBO> jbxxbBOS) ;
}
