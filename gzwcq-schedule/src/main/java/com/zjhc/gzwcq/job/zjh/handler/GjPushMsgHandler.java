package com.zjhc.gzwcq.job.zjh.handler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zjhc.gzwcq.job.zjh.service.api.IGjPushMsgService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/05/27:14:55:19
 **/
@Component
public class GjPushMsgHandler {
    private static Logger logger = LoggerFactory.getLogger(GjPushMsgHandler.class);
    @Autowired
    private IGjPushMsgService  gjPushMsgService;
    @XxlJob(value = "gjPushMsgHandler", init = "init", destroy = "destroy")
    public void pushMsg() {
        logger.info("开始推送消息");
        long startTime = System.currentTimeMillis();
        try {
            //TODO:推送消息
            gjPushMsgService.pushMsg();
        } catch (Exception e) {
            logger.error("推送消息未知异常：" + e.getMessage());
        }
        long endTime = System.currentTimeMillis();
        logger.info("推送消息结束 用时：" + (endTime - startTime) / 1000);
    }
    public void init() {
        logger.info("init");
    }

    public void destroy() {
        logger.info("destroy");
    }
}
